import { z } from "zod";
export declare const venn: {
    schema: {
        data: z.<PERSON><z.ZodObject<{
            label: z.ZodOptional<z.ZodString>;
            value: z.ZodNumber;
            sets: z.<PERSON><PERSON><z.ZodString, "many">;
        }, "strip", z.ZodTypeAny, {
            value: number;
            sets: string[];
            label?: string | undefined;
        }, {
            value: number;
            sets: string[];
            label?: string | undefined;
        }>, "atleastone">;
        theme: z.ZodDefault<z.ZodOptional<z.ZodEnum<["default", "academy"]>>>;
        width: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
        height: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
        title: z.<PERSON>odDefault<z.ZodOptional<z.ZodString>>;
    };
    tool: {
        name: string;
        description: string;
        inputSchema: import("zod-to-json-schema").JsonSchema7Type & {
            $schema?: string | undefined;
            definitions?: {
                [key: string]: import("zod-to-json-schema").JsonSchema7Type;
            } | undefined;
        };
    };
};
