"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.radar = void 0;
const zod_1 = require("zod");
const utils_1 = require("../utils/index.js");
const base_1 = require("./base.js");
// Radar chart data schema
const data = zod_1.z.object({
    name: zod_1.z.string(),
    value: zod_1.z.number(),
    group: zod_1.z.string().optional(),
});
// Radar chart input schema
const schema = {
    data: zod_1.z
        .array(data)
        .describe("Data for radar chart, such as, [{ name: 'Design', value: 70 }].")
        .nonempty({ message: "Radar chart data cannot be empty." }),
    theme: base_1.ThemeSchema,
    width: base_1.WidthSchema,
    height: base_1.HeightSchema,
    title: base_1.TitleSchema,
};
// Radar chart tool descriptor
const tool = {
    name: "generate_radar_chart",
    description: "Generate a radar chart to display multidimensional data (four dimensions or more), such as, evaluate Huawei and Apple phones in terms of five dimensions: ease of use, functionality, camera, benchmark scores, and battery life.",
    inputSchema: (0, utils_1.zodToJsonSchema)(schema),
};
exports.radar = {
    schema,
    tool,
};
