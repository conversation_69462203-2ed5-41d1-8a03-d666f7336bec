# MCP Chart Visualization Server 📊

A comprehensive setup of the **AntV MCP Server Chart** - a powerful Model Context Protocol server for generating 25+ types of professional charts and visualizations using AI assistants.

## 🎯 What is this?

This is a **Model Context Protocol (MCP) server** that bridges AI assistants with data visualization capabilities. It allows you to:

- **Generate professional charts** by simply describing what you want in natural language
- **Create 25+ different chart types** including basic charts, statistical visualizations, and advanced diagrams
- **Integrate with AI assistants** like <PERSON>, ChatGPT, and other MCP-compatible tools
- **Export high-quality visualizations** for presentations, reports, and analysis

## 📊 Supported Chart Types

### Basic Charts
- **Line Charts** - Trends over time
- **Bar/Column Charts** - Category comparisons  
- **Area Charts** - Cumulative data trends
- **Pie Charts** - Proportional data
- **Scatter Plots** - Relationship between variables

### Statistical Charts
- **Histogram** - Data distribution
- **Boxplot** - Statistical summaries
- **Violin Plot** - Detailed distribution analysis
- **Radar Chart** - Multi-dimensional data

### Advanced Visualizations
- **Sankey Diagrams** - Flow visualization
- **Treemap** - Hierarchical data
- **Word Clouds** - Text frequency analysis
- **Network Graphs** - Relationship mapping
- **Funnel Charts** - Process analysis

### Specialized Charts
- **Mind Maps** - Thought organization
- **Flowcharts** - Process visualization
- **Fishbone Diagrams** - Root cause analysis
- **Organization Charts** - Hierarchical structures
- **Venn Diagrams** - Set relationships

### Geographic Visualizations
- **District Maps** - Administrative regions (China)
- **Pin Maps** - Location markers
- **Path Maps** - Route visualization

## 🚀 Quick Start

### Prerequisites
- **Node.js** v16+ (we have v22.16.0 ✅)
- **npm** v8+ (we have v10.9.2 ✅)

### 1. Start the Server

#### For AI Assistant Integration (Claude Desktop, etc.)
```powershell
# Start with stdio transport (default)
.\start-server.ps1
```

#### For Web/HTTP Integration
```powershell
# Start with SSE transport on port 3001
.\start-server.ps1 -Transport sse

# Start with custom port
.\start-server.ps1 -Transport sse -Port 8080

# Start with streamable transport
.\start-server.ps1 -Transport streamable
```

### 2. Configure Your AI Assistant

#### Claude Desktop Configuration
Copy the configuration from `config/claude-desktop-config.json` to your Claude Desktop settings:

```json
{
  "mcpServers": {
    "mcp-server-chart": {
      "command": "node",
      "args": [
        "C:\\Users\\<USER>\\Desktop\\MCP\\MCP-Chart-Visualization-Server\\mcp-server-chart\\build\\index.js"
      ],
      "env": {
        "NODE_ENV": "production"
      }
    }
  }
}
```

## 📁 Project Structure

```
MCP-Chart-Visualization-Server/
├── mcp-server-chart/          # Main server code (cloned from antvis/mcp-server-chart)
│   ├── build/                 # Compiled TypeScript files
│   ├── src/                   # Source TypeScript files
│   ├── __tests__/             # Test files
│   └── package.json           # Dependencies and scripts
├── config/                    # Configuration files
│   ├── claude-desktop-config.json    # Claude Desktop MCP config
│   └── sse-server-config.json        # SSE server configuration
├── docs/                      # Documentation
├── examples/                  # Usage examples
├── tests/                     # Additional tests
├── start-server.ps1          # PowerShell startup script
└── README.md                 # This file
```

## 🛠️ Usage Examples

### Example 1: Creating a Line Chart
```
"Create a line chart showing monthly sales data:
January: $10,000
February: $12,000  
March: $15,000
April: $13,000
May: $18,000"
```

### Example 2: Creating a Pie Chart
```
"Generate a pie chart for our market share:
Company A: 35%
Company B: 25%
Company C: 20%
Others: 20%"
```

### Example 3: Creating a Flowchart
```
"Create a flowchart for the user registration process:
1. User visits signup page
2. User enters email and password
3. System validates input
4. If valid, create account
5. Send confirmation email
6. User confirms email
7. Account activated"
```

## ⚙️ Configuration Options

### Environment Variables
- `VIS_REQUEST_SERVER` - Custom chart generation service URL
- `SERVICE_ID` - Service identifier for chart generation records
- `DISABLED_TOOLS` - Comma-separated list of tools to disable

### Transport Options
- **stdio** - For direct AI assistant integration
- **sse** - Server-Sent Events for web applications
- **streamable** - Streamable transport for real-time applications

## 🧪 Testing

### Run Tests
```powershell
cd mcp-server-chart
npm test
```

### Manual Testing
```powershell
# Test server help
node build/index.js --help

# Test with different transports
node build/index.js --transport sse --port 3001
```

## 🔧 Development

### Build from Source
```powershell
cd mcp-server-chart
npm install
npm run build
```

### Development Mode
```powershell
npm run start
```

## 📚 Resources

- **Original Repository**: [antvis/mcp-server-chart](https://github.com/antvis/mcp-server-chart)
- **AntV Documentation**: [https://antv.antgroup.com/](https://antv.antgroup.com/)
- **MCP Specification**: [Model Context Protocol](https://modelcontextprotocol.io/)

## 🎉 What Makes This Awesome?

1. **Professional Quality** - Uses AntV, a leading visualization library
2. **AI-Powered** - Natural language chart generation
3. **Comprehensive** - 25+ chart types for any visualization need
4. **Easy Integration** - Works with popular AI assistants
5. **Flexible Deployment** - Multiple transport options
6. **Well-Organized** - Clean project structure and documentation

## 🚀 Next Steps

1. **Test chart generation** with your AI assistant
2. **Explore different chart types** for your data
3. **Customize configurations** for your specific needs
4. **Integrate with your workflow** for automated reporting

---

**Ready to create amazing visualizations with AI? Start the server and begin generating charts!** 🎨📈
