"use strict";
/**
 * This is a copy of the InMemoryEventStore from the typescript-sdk.
 * reference: https://github.com/modelcontextprotocol/typescript-sdk/blob/main/src/examples/shared/inMemoryEventStore.ts
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InMemoryEventStore = void 0;
/**
 * Simple in-memory implementation of the EventStore interface for resumability.
 * This is primarily intended for examples and testing, not for production use.
 * where a persistent storage solution would be more appropriate.
 * see more details: https://modelcontextprotocol.io/specification/2025-03-26/basic/transports#resumability-and-redelivery.
 */
class InMemoryEventStore {
    constructor() {
        this.events = new Map();
    }
    /**
     * Generates a unique event ID for a given stream ID
     */
    generateEventId(streamId) {
        return `${streamId}_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
    }
    /**
     * Extracts the stream ID from an event ID
     */
    getStreamIdFromEventId(eventId) {
        const parts = eventId.split("_");
        return parts.length > 0 ? parts[0] : "";
    }
    /**
     * Stores an event with a generated event ID
     * Implements EventStore.storeEvent
     */
    storeEvent(streamId, message) {
        return __awaiter(this, void 0, void 0, function* () {
            const eventId = this.generateEventId(streamId);
            this.events.set(eventId, { streamId, message });
            return eventId;
        });
    }
    /**
     * Replays events that occurred after a specific event ID
     * Implements EventStore.replayEventsAfter
     */
    replayEventsAfter(lastEventId_1, _a) {
        return __awaiter(this, arguments, void 0, function* (lastEventId, { send, }) {
            if (!lastEventId || !this.events.has(lastEventId)) {
                return "";
            }
            // Extract the stream ID from the event ID
            const streamId = this.getStreamIdFromEventId(lastEventId);
            if (!streamId) {
                return "";
            }
            let foundLastEvent = false;
            // Sort events by eventId for chronological ordering
            const sortedEvents = [...this.events.entries()].sort((a, b) => a[0].localeCompare(b[0]));
            for (const [eventId, { streamId: eventStreamId, message },] of sortedEvents) {
                // Only include events from the same stream
                if (eventStreamId !== streamId) {
                    continue;
                }
                // Start sending events after we find the lastEventId
                if (eventId === lastEventId) {
                    foundLastEvent = true;
                    continue;
                }
                if (foundLastEvent) {
                    yield send(eventId, message);
                }
            }
            return streamId;
        });
    }
}
exports.InMemoryEventStore = InMemoryEventStore;
