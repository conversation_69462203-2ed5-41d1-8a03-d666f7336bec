import { z } from "zod";
export declare const districtMap: {
    schema: {
        title: z.ZodString;
        data: z.ZodObject<{
            name: z.ZodString;
            style: z.ZodOptional<z.ZodObject<{
                fillColor: z.ZodOptional<z.ZodString>;
            }, "strip", z.ZodTypeAny, {
                fillColor?: string | undefined;
            }, {
                fillColor?: string | undefined;
            }>>;
            colors: z.ZodOptional<z.ZodDefault<z.ZodArray<z.ZodString, "many">>>;
            dataType: z.ZodOptional<z.ZodEnum<["number", "enum"]>>;
            dataLabel: z.ZodOptional<z.ZodString>;
            dataValue: z.ZodOptional<z.ZodString>;
            dataValueUnit: z.ZodOptional<z.ZodString>;
            showAllSubdistricts: z.ZodDefault<z.ZodOptional<z.ZodBoolean>>;
            subdistricts: z.ZodOptional<z.ZodArray<z.ZodObject<{
                name: z.ZodString;
                dataValue: z.ZodOptional<z.ZodString>;
                style: z.ZodOptional<z.ZodObject<{
                    fillColor: z.ZodOptional<z.ZodString>;
                }, "strip", z.ZodTypeAny, {
                    fillColor?: string | undefined;
                }, {
                    fillColor?: string | undefined;
                }>>;
            }, "strip", z.ZodTypeAny, {
                name: string;
                style?: {
                    fillColor?: string | undefined;
                } | undefined;
                dataValue?: string | undefined;
            }, {
                name: string;
                style?: {
                    fillColor?: string | undefined;
                } | undefined;
                dataValue?: string | undefined;
            }>, "many">>;
        }, "strip", z.ZodTypeAny, {
            name: string;
            showAllSubdistricts: boolean;
            style?: {
                fillColor?: string | undefined;
            } | undefined;
            colors?: string[] | undefined;
            dataType?: "number" | "enum" | undefined;
            dataLabel?: string | undefined;
            dataValue?: string | undefined;
            dataValueUnit?: string | undefined;
            subdistricts?: {
                name: string;
                style?: {
                    fillColor?: string | undefined;
                } | undefined;
                dataValue?: string | undefined;
            }[] | undefined;
        }, {
            name: string;
            style?: {
                fillColor?: string | undefined;
            } | undefined;
            colors?: string[] | undefined;
            dataType?: "number" | "enum" | undefined;
            dataLabel?: string | undefined;
            dataValue?: string | undefined;
            dataValueUnit?: string | undefined;
            showAllSubdistricts?: boolean | undefined;
            subdistricts?: {
                name: string;
                style?: {
                    fillColor?: string | undefined;
                } | undefined;
                dataValue?: string | undefined;
            }[] | undefined;
        }>;
        width: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
        height: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
    };
    tool: {
        name: string;
        description: string;
        inputSchema: import("zod-to-json-schema").JsonSchema7Type & {
            $schema?: string | undefined;
            definitions?: {
                [key: string]: import("zod-to-json-schema").JsonSchema7Type;
            } | undefined;
        };
    };
};
