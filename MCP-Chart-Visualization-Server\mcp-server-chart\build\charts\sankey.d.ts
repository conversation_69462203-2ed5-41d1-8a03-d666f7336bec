import { z } from "zod";
export declare const sankey: {
    schema: {
        data: z.<PERSON><PERSON>y<z.ZodObject<{
            source: z.ZodString;
            target: z.ZodString;
            value: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            value: number;
            target: string;
            source: string;
        }, {
            value: number;
            target: string;
            source: string;
        }>, "atleastone">;
        nodeAlign: z.<PERSON><z.ZodOptional<z.ZodEnum<["left", "right", "justify", "center"]>>>;
        theme: z.ZodDefault<z.ZodOptional<z.ZodEnum<["default", "academy"]>>>;
        width: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
        height: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
        title: z.ZodDefault<z.ZodOptional<z.ZodString>>;
    };
    tool: {
        name: string;
        description: string;
        inputSchema: import("zod-to-json-schema").JsonSchema7Type & {
            $schema?: string | undefined;
            definitions?: {
                [key: string]: import("zod-to-json-schema").JsonSchema7Type;
            } | undefined;
        };
    };
};
