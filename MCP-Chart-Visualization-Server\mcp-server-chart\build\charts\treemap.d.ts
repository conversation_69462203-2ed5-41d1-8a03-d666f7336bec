import { z } from "zod";
type TreemapDataType = {
    name: string;
    value: number;
    children?: TreemapDataType[];
};
export declare const treemap: {
    schema: {
        data: z.ZodArray<z.ZodType<TreemapDataType, z.ZodTypeDef, TreemapDataType>, "atleastone">;
        theme: z.<PERSON><PERSON><PERSON><z.ZodOptional<z.ZodEnum<["default", "academy"]>>>;
        width: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
        height: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
        title: z.ZodDefault<z.ZodOptional<z.ZodString>>;
    };
    tool: {
        name: string;
        description: string;
        inputSchema: import("zod-to-json-schema").JsonSchema7Type & {
            $schema?: string | undefined;
            definitions?: {
                [key: string]: import("zod-to-json-schema").JsonSchema7Type;
            } | undefined;
        };
    };
};
export {};
