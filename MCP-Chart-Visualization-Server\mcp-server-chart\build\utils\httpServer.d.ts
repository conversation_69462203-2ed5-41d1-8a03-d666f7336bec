import http from "node:http";
import type { IncomingMessage, ServerResponse } from "node:http";
/**
 * Interface for request handlers that will be passed to the server factory
 */
export interface RequestHandlers {
    /**
     * Main handler for HTTP requests
     */
    handleRequest: (req: IncomingMessage, res: ServerResponse) => Promise<void>;
    /**
     * Custom cleanup function to be called when the server is shutting down
     */
    cleanup?: () => void;
    /**
     * Server type name for logging purposes
     */
    serverType: string;
}
/**
 * Creates a base HTTP server with common functionality
 */
export declare function createBaseHttpServer(port: number, endpoint: string, handlers: RequestHandlers): http.Server;
