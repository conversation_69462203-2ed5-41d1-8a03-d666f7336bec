import { z } from "zod";
export declare const ThemeSchema: z.ZodDefault<z.ZodOptional<z.ZodEnum<["default", "academy"]>>>;
export declare const WidthSchema: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
export declare const HeightSchema: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
export declare const TitleSchema: z.ZodDefault<z.ZodOptional<z.ZodString>>;
export declare const AxisXTitleSchema: z.ZodDefault<z.ZodOptional<z.ZodString>>;
export declare const AxisYTitleSchema: z.ZodDefault<z.ZodOptional<z.ZodString>>;
export declare const NodeSchema: z.ZodObject<{
    name: z.ZodString;
}, "strip", z.ZodTypeAny, {
    name: string;
}, {
    name: string;
}>;
export declare const EdgeSchema: z.ZodObject<{
    source: z.ZodString;
    target: z.ZodString;
    name: z.ZodDefault<z.ZodOptional<z.ZodString>>;
}, "strip", z.ZodTypeAny, {
    name: string;
    target: string;
    source: string;
}, {
    target: string;
    source: string;
    name?: string | undefined;
}>;
export declare const MapTitleSchema: z.ZodString;
export declare const MapWidthSchema: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
export declare const MapHeightSchema: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
export declare const POIsSchema: z.ZodArray<z.ZodString, "atleastone">;
