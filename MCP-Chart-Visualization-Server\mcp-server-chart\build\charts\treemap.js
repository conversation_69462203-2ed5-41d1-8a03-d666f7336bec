"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.treemap = void 0;
const zod_1 = require("zod");
const utils_1 = require("../utils/index.js");
const base_1 = require("./base.js");
// Define recursive schema for hierarchical data
const TreeNodeSchema = zod_1.z.lazy(() => zod_1.z.object({
    name: zod_1.z.string(),
    value: zod_1.z.number(),
    children: zod_1.z.array(TreeNodeSchema).optional(),
}));
// Treemap chart input schema
const schema = {
    data: zod_1.z
        .array(TreeNodeSchema)
        .describe("Data for treemap chart, such as, [{ name: 'Design', value: 70, children: [{ name: 'Tech', value: 20 }] }].")
        .nonempty({ message: "Treemap chart data cannot be empty." }),
    theme: base_1.ThemeSchema,
    width: base_1.WidthSchema,
    height: base_1.HeightSchema,
    title: base_1.TitleSchema,
};
// Treemap chart tool descriptor
const tool = {
    name: "generate_treemap_chart",
    description: "Generate a treemap chart to display hierarchical data and can intuitively show comparisons between items at the same level, such as, show disk space usage with treemap.",
    inputSchema: (0, utils_1.zodToJsonSchema)(schema),
};
exports.treemap = {
    schema,
    tool,
};
