"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports["word-cloud"] = exports.violin = exports.venn = exports.treemap = exports.scatter = exports.sankey = exports.radar = exports["pin-map"] = exports.pie = exports["path-map"] = exports["organization-chart"] = exports["network-graph"] = exports["mind-map"] = exports.liquid = exports.line = exports.histogram = exports.funnel = exports["flow-diagram"] = exports["fishbone-diagram"] = exports["dual-axes"] = exports["district-map"] = exports.column = exports.boxplot = exports.bar = exports.area = void 0;
/**
 * export all charts as named exports to match the chart type
 */
var area_1 = require("./area.js");
Object.defineProperty(exports, "area", { enumerable: true, get: function () { return area_1.area; } });
var bar_1 = require("./bar.js");
Object.defineProperty(exports, "bar", { enumerable: true, get: function () { return bar_1.bar; } });
var boxplot_1 = require("./boxplot.js");
Object.defineProperty(exports, "boxplot", { enumerable: true, get: function () { return boxplot_1.boxplot; } });
var column_1 = require("./column.js");
Object.defineProperty(exports, "column", { enumerable: true, get: function () { return column_1.column; } });
var district_map_1 = require("./district-map.js");
Object.defineProperty(exports, "district-map", { enumerable: true, get: function () { return district_map_1.districtMap; } });
var dual_axes_1 = require("./dual-axes.js");
Object.defineProperty(exports, "dual-axes", { enumerable: true, get: function () { return dual_axes_1.dualAxes; } });
var fishbone_diagram_1 = require("./fishbone-diagram.js");
Object.defineProperty(exports, "fishbone-diagram", { enumerable: true, get: function () { return fishbone_diagram_1.fishboneDiagram; } });
var flow_diagram_1 = require("./flow-diagram.js");
Object.defineProperty(exports, "flow-diagram", { enumerable: true, get: function () { return flow_diagram_1.flowDiagram; } });
var funnel_1 = require("./funnel.js");
Object.defineProperty(exports, "funnel", { enumerable: true, get: function () { return funnel_1.funnel; } });
var histogram_1 = require("./histogram.js");
Object.defineProperty(exports, "histogram", { enumerable: true, get: function () { return histogram_1.histogram; } });
var line_1 = require("./line.js");
Object.defineProperty(exports, "line", { enumerable: true, get: function () { return line_1.line; } });
var liquid_1 = require("./liquid.js");
Object.defineProperty(exports, "liquid", { enumerable: true, get: function () { return liquid_1.liquid; } });
var mind_map_1 = require("./mind-map.js");
Object.defineProperty(exports, "mind-map", { enumerable: true, get: function () { return mind_map_1.mindMap; } });
var network_graph_1 = require("./network-graph.js");
Object.defineProperty(exports, "network-graph", { enumerable: true, get: function () { return network_graph_1.networkGraph; } });
var organization_chart_1 = require("./organization-chart.js");
Object.defineProperty(exports, "organization-chart", { enumerable: true, get: function () { return organization_chart_1.organizationChart; } });
var path_map_1 = require("./path-map.js");
Object.defineProperty(exports, "path-map", { enumerable: true, get: function () { return path_map_1.pathMap; } });
var pie_1 = require("./pie.js");
Object.defineProperty(exports, "pie", { enumerable: true, get: function () { return pie_1.pie; } });
var pin_map_1 = require("./pin-map.js");
Object.defineProperty(exports, "pin-map", { enumerable: true, get: function () { return pin_map_1.pinMap; } });
var radar_1 = require("./radar.js");
Object.defineProperty(exports, "radar", { enumerable: true, get: function () { return radar_1.radar; } });
var sankey_1 = require("./sankey.js");
Object.defineProperty(exports, "sankey", { enumerable: true, get: function () { return sankey_1.sankey; } });
var scatter_1 = require("./scatter.js");
Object.defineProperty(exports, "scatter", { enumerable: true, get: function () { return scatter_1.scatter; } });
var treemap_1 = require("./treemap.js");
Object.defineProperty(exports, "treemap", { enumerable: true, get: function () { return treemap_1.treemap; } });
var venn_1 = require("./venn.js");
Object.defineProperty(exports, "venn", { enumerable: true, get: function () { return venn_1.venn; } });
var violin_1 = require("./violin.js");
Object.defineProperty(exports, "violin", { enumerable: true, get: function () { return violin_1.violin; } });
var word_cloud_1 = require("./word-cloud.js");
Object.defineProperty(exports, "word-cloud", { enumerable: true, get: function () { return word_cloud_1.wordCloud; } });
