# Troubleshooting Guide 🔧

Common issues and solutions for the MCP Chart Visualization Server.

## 🚨 Common Issues

### 1. Server Won't Start

#### Problem: "node: command not found"
**Solution**: Install Node.js
```powershell
# Download and install Node.js from https://nodejs.org/
# Or use a package manager like <PERSON><PERSON>:
choco install nodejs
```

#### Problem: "Cannot find module" errors
**Solution**: Install dependencies
```powershell
cd mcp-server-chart
npm install
```

#### Problem: "Permission denied" or execution policy errors
**Solution**: Set PowerShell execution policy
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### 2. Build Issues

#### Problem: TypeScript compilation errors
**Solution**: Clean and rebuild
```powershell
cd mcp-server-chart
rm -rf build/
npm run build
```

#### Problem: "tsc: command not found"
**Solution**: Install TypeScript globally or use npx
```powershell
npm install -g typescript
# OR use npx (recommended)
npx tsc --version
```

### 3. AI Assistant Integration

#### Problem: <PERSON> doesn't recognize the server
**Solution**: Check configuration file location
- **Windows**: `%APPDATA%\Claude\claude_desktop_config.json`
- **macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`

#### Problem: "Server failed to start" in Claude
**Solution**: Verify paths in configuration
```json
{
  "mcpServers": {
    "mcp-server-chart": {
      "command": "node",
      "args": [
        "C:\\Users\\<USER>\\Desktop\\MCP\\MCP-Chart-Visualization-Server\\mcp-server-chart\\build\\index.js"
      ]
    }
  }
}
```

### 4. Chart Generation Issues

#### Problem: Charts not generating or showing errors
**Solution**: Check internet connection and service availability
- The default service requires internet access
- Consider using a private deployment if needed

#### Problem: "Tool not found" errors
**Solution**: Verify server is properly connected
```powershell
# Test server directly
node build/index.js --help
```

### 5. Network and Port Issues

#### Problem: Port already in use
**Solution**: Use a different port
```powershell
.\start-server.ps1 -Transport sse -Port 8080
```

#### Problem: Cannot access SSE endpoint
**Solution**: Check firewall and network settings
```powershell
# Test if port is accessible
Test-NetConnection -ComputerName localhost -Port 3001
```

## 🔍 Diagnostic Commands

### Check System Requirements
```powershell
# Check Node.js version (should be 16+)
node --version

# Check npm version
npm --version

# Check if build exists
Test-Path "mcp-server-chart\build\index.js"
```

### Test Server Functionality
```powershell
# Test help command
node mcp-server-chart\build\index.js --help

# Test with stdio transport
node mcp-server-chart\build\index.js

# Test with SSE transport
node mcp-server-chart\build\index.js --transport sse --port 3001
```

### Verify Dependencies
```powershell
cd mcp-server-chart
npm list --depth=0
```

## 📋 Configuration Validation

### Claude Desktop Config Checklist
- [ ] File exists in correct location
- [ ] JSON syntax is valid
- [ ] Paths use forward slashes or escaped backslashes
- [ ] Node.js path is correct
- [ ] Build directory exists

### Server Config Checklist
- [ ] Port is not in use by another application
- [ ] Firewall allows the specified port
- [ ] Environment variables are set correctly
- [ ] Transport type is supported

## 🛠️ Advanced Troubleshooting

### Enable Debug Logging
```powershell
# Set environment variable for detailed logging
$env:DEBUG = "*"
node mcp-server-chart\build\index.js
```

### Check Process Status
```powershell
# List Node.js processes
Get-Process -Name node -ErrorAction SilentlyContinue

# Check port usage
netstat -an | findstr :3001
```

### Validate JSON Configuration
```powershell
# Test JSON syntax
Get-Content config\claude-desktop-config.json | ConvertFrom-Json
```

## 🔄 Reset and Reinstall

### Complete Reset
```powershell
# Remove node_modules and reinstall
cd mcp-server-chart
Remove-Item -Recurse -Force node_modules
Remove-Item -Force package-lock.json
npm install
npm run build
```

### Fresh Clone
```powershell
# If all else fails, start fresh
cd ..
Remove-Item -Recurse -Force MCP-Chart-Visualization-Server
git clone https://github.com/antvis/mcp-server-chart.git
cd mcp-server-chart
npm install
npm run build
```

## 📞 Getting Help

### Log Collection
When reporting issues, include:
1. **System Information**:
   - Windows version
   - Node.js version
   - npm version

2. **Error Messages**:
   - Complete error output
   - Stack traces if available

3. **Configuration**:
   - MCP server configuration
   - Environment variables

4. **Steps to Reproduce**:
   - Exact commands used
   - Expected vs actual behavior

### Useful Commands for Bug Reports
```powershell
# System info
systeminfo | findstr /B /C:"OS Name" /C:"OS Version"
node --version
npm --version

# Server status
node mcp-server-chart\build\index.js --help

# Configuration validation
Get-Content config\claude-desktop-config.json
```

## ✅ Quick Health Check

Run this script to verify everything is working:

```powershell
Write-Host "=== MCP Chart Server Health Check ===" -ForegroundColor Green

# Check Node.js
if (Get-Command node -ErrorAction SilentlyContinue) {
    Write-Host "✅ Node.js: $(node --version)" -ForegroundColor Green
} else {
    Write-Host "❌ Node.js not found" -ForegroundColor Red
}

# Check npm
if (Get-Command npm -ErrorAction SilentlyContinue) {
    Write-Host "✅ npm: $(npm --version)" -ForegroundColor Green
} else {
    Write-Host "❌ npm not found" -ForegroundColor Red
}

# Check build
if (Test-Path "mcp-server-chart\build\index.js") {
    Write-Host "✅ Server build exists" -ForegroundColor Green
} else {
    Write-Host "❌ Server build missing - run 'npm run build'" -ForegroundColor Red
}

# Check dependencies
if (Test-Path "mcp-server-chart\node_modules") {
    Write-Host "✅ Dependencies installed" -ForegroundColor Green
} else {
    Write-Host "❌ Dependencies missing - run 'npm install'" -ForegroundColor Red
}

Write-Host "=== Health Check Complete ===" -ForegroundColor Green
```

---

**Still having issues? Check the [GitHub repository](https://github.com/antvis/mcp-server-chart) for the latest updates and community support!** 🚀
