import { z } from "zod";
export declare const flowDiagram: {
    schema: {
        data: z.ZodEffects<z.ZodObject<{
            nodes: z.Z<PERSON>y<z.ZodObject<{
                name: z.ZodString;
            }, "strip", z.<PERSON>odType<PERSON>ny, {
                name: string;
            }, {
                name: string;
            }>, "atleastone">;
            edges: z.Z<PERSON>y<z.ZodObject<{
                source: z.ZodString;
                target: z.ZodString;
                name: z.ZodDefault<z.ZodOptional<z.ZodString>>;
            }, "strip", z.ZodTypeAny, {
                name: string;
                target: string;
                source: string;
            }, {
                target: string;
                source: string;
                name?: string | undefined;
            }>, "many">;
        }, "strip", z.ZodType<PERSON>ny, {
            nodes: [{
                name: string;
            }, ...{
                name: string;
            }[]];
            edges: {
                name: string;
                target: string;
                source: string;
            }[];
        }, {
            nodes: [{
                name: string;
            }, ...{
                name: string;
            }[]];
            edges: {
                target: string;
                source: string;
                name?: string | undefined;
            }[];
        }>, {
            nodes: [{
                name: string;
            }, ...{
                name: string;
            }[]];
            edges: {
                name: string;
                target: string;
                source: string;
            }[];
        }, {
            nodes: [{
                name: string;
            }, ...{
                name: string;
            }[]];
            edges: {
                target: string;
                source: string;
                name?: string | undefined;
            }[];
        }>;
        theme: z.ZodDefault<z.ZodOptional<z.ZodEnum<["default", "academy"]>>>;
        width: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
        height: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
    };
    tool: {
        name: string;
        description: string;
        inputSchema: import("zod-to-json-schema").JsonSchema7Type & {
            $schema?: string | undefined;
            definitions?: {
                [key: string]: import("zod-to-json-schema").JsonSchema7Type;
            } | undefined;
        };
    };
};
