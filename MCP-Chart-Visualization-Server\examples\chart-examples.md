# Chart Generation Examples 📊

This document provides practical examples of how to generate different types of charts using the MCP Chart Server with AI assistants.

## 📈 Basic Charts

### Line Chart Example
**Prompt**: "Create a line chart showing our website traffic over the last 6 months"

**Data to provide**:
```
January: 15,000 visitors
February: 18,500 visitors  
March: 22,000 visitors
April: 19,800 visitors
May: 25,600 visitors
June: 28,200 visitors
```

### Bar Chart Example
**Prompt**: "Generate a bar chart comparing quarterly sales performance"

**Data to provide**:
```
Q1 2024: $125,000
Q2 2024: $148,000
Q3 2024: $162,000
Q4 2024: $189,000
```

### Pie Chart Example
**Prompt**: "Create a pie chart showing our customer demographics by age group"

**Data to provide**:
```
18-25: 22%
26-35: 35%
36-45: 28%
46-55: 12%
55+: 3%
```

## 📊 Statistical Charts

### Histogram Example
**Prompt**: "Create a histogram showing the distribution of employee salaries"

**Data to provide**:
```
$30k-40k: 5 employees
$40k-50k: 12 employees
$50k-60k: 18 employees
$60k-70k: 15 employees
$70k-80k: 8 employees
$80k-90k: 4 employees
$90k+: 2 employees
```

### Boxplot Example
**Prompt**: "Generate a boxplot to analyze response times across different servers"

**Data to provide**:
```
Server A response times (ms): [120, 135, 142, 156, 168, 175, 189, 201, 215, 230]
Server B response times (ms): [98, 105, 118, 125, 132, 145, 158, 167, 180, 195]
Server C response times (ms): [145, 152, 168, 175, 182, 198, 205, 220, 235, 250]
```

### Radar Chart Example
**Prompt**: "Create a radar chart comparing product features across competitors"

**Data to provide**:
```
Our Product:
- Performance: 8/10
- Price: 7/10
- Features: 9/10
- Support: 8/10
- Usability: 9/10

Competitor A:
- Performance: 7/10
- Price: 9/10
- Features: 6/10
- Support: 7/10
- Usability: 7/10
```

## 🔄 Flow and Process Charts

### Flowchart Example
**Prompt**: "Create a flowchart for our customer support ticket process"

**Process steps**:
```
1. Customer submits ticket
2. System assigns ticket ID
3. Is it urgent? (Decision)
   - Yes: Route to priority queue
   - No: Route to standard queue
4. Agent reviews ticket
5. Can agent resolve? (Decision)
   - Yes: Provide solution
   - No: Escalate to specialist
6. Customer satisfied? (Decision)
   - Yes: Close ticket
   - No: Continue investigation
7. End
```

### Fishbone Diagram Example
**Prompt**: "Generate a fishbone diagram to analyze causes of website downtime"

**Categories and causes**:
```
Technology:
- Server hardware failure
- Network connectivity issues
- Software bugs
- Database overload

Process:
- Inadequate monitoring
- Poor deployment procedures
- Lack of backup systems

People:
- Insufficient training
- Communication gaps
- Understaffing during peak hours

Environment:
- Power outages
- Data center issues
- Third-party service failures
```

## 🌐 Advanced Visualizations

### Sankey Diagram Example
**Prompt**: "Create a Sankey diagram showing our sales funnel conversion"

**Flow data**:
```
Website Visitors (10,000) →
  - Signed up for newsletter (3,000)
  - Viewed product page (5,000)
  - Added to cart (1,500)
  - Completed purchase (800)
  - Became repeat customer (200)
```

### Network Graph Example
**Prompt**: "Generate a network graph showing relationships between team members"

**Relationships**:
```
Nodes: Alice (Manager), Bob (Developer), Carol (Designer), Dave (QA), Eve (Marketing)

Connections:
- Alice → Bob (manages)
- Alice → Carol (manages)
- Alice → Dave (manages)
- Bob ↔ Carol (collaborates)
- Carol ↔ Eve (collaborates)
- Bob ↔ Dave (collaborates)
```

### Treemap Example
**Prompt**: "Create a treemap showing budget allocation across departments"

**Data**:
```
Engineering: $500,000
  - Frontend: $200,000
  - Backend: $180,000
  - DevOps: $120,000

Marketing: $300,000
  - Digital: $150,000
  - Content: $100,000
  - Events: $50,000

Sales: $200,000
  - Inside Sales: $120,000
  - Field Sales: $80,000
```

## 📝 Organizational Charts

### Organization Chart Example
**Prompt**: "Generate an organization chart for our development team"

**Structure**:
```
CTO (Sarah)
├── Engineering Manager (Mike)
│   ├── Senior Developer (Alex)
│   ├── Developer (Lisa)
│   └── Junior Developer (Tom)
├── QA Manager (Rachel)
│   ├── QA Lead (David)
│   └── QA Tester (Emma)
└── DevOps Engineer (Chris)
```

### Mind Map Example
**Prompt**: "Create a mind map for planning our product launch strategy"

**Central topic**: Product Launch Strategy

**Branches**:
```
Marketing
├── Social Media Campaign
├── Press Release
├── Influencer Partnerships
└── Content Marketing

Sales
├── Sales Training
├── Pricing Strategy
├── Channel Partners
└── Customer Demos

Product
├── Feature Completion
├── Quality Assurance
├── Documentation
└── User Onboarding

Operations
├── Inventory Management
├── Customer Support
├── Fulfillment Process
└── Analytics Setup
```

## 💡 Tips for Better Charts

1. **Be Specific**: Provide exact data values when possible
2. **Context Matters**: Explain what the chart should communicate
3. **Choose Appropriate Types**: Match chart type to your data story
4. **Label Clearly**: Specify axis labels, titles, and legends
5. **Color Preferences**: Mention if you have specific color requirements

## 🎯 Common Use Cases

- **Business Reports**: Sales data, KPIs, performance metrics
- **Data Analysis**: Statistical distributions, correlations, trends
- **Process Documentation**: Workflows, procedures, decision trees
- **Project Planning**: Timelines, resource allocation, dependencies
- **Presentations**: Executive summaries, stakeholder updates

---

**Ready to create your own charts? Start with these examples and customize them for your specific needs!** 🚀
