import { z } from "zod";
export declare const pie: {
    schema: {
        data: z.<PERSON><z.ZodObject<{
            category: z.ZodString;
            value: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            value: number;
            category: string;
        }, {
            value: number;
            category: string;
        }>, "atleastone">;
        innerRadius: z.<PERSON><PERSON><PERSON><z.ZodNumber>;
        theme: z.<PERSON><PERSON><PERSON><z.ZodOptional<z.ZodEnum<["default", "academy"]>>>;
        width: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
        height: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
        title: z.<PERSON>od<PERSON><PERSON>ault<z.ZodOptional<z.ZodString>>;
    };
    tool: {
        name: string;
        description: string;
        inputSchema: import("zod-to-json-schema").JsonSchema7Type & {
            $schema?: string | undefined;
            definitions?: {
                [key: string]: import("zod-to-json-schema").JsonSchema7Type;
            } | undefined;
        };
    };
};
