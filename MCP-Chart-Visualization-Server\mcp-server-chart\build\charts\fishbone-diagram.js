"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.fishboneDiagram = void 0;
const zod_1 = require("zod");
const utils_1 = require("../utils/index.js");
const validator_1 = require("../utils/validator.js");
const base_1 = require("./base.js");
// Fishbone node schema
const FishboneNodeSchema = zod_1.z.lazy(() => zod_1.z.object({
    name: zod_1.z.string(),
    children: zod_1.z.array(FishboneNodeSchema).optional(),
}));
// Fishbone diagram input schema
const schema = {
    data: FishboneNodeSchema.describe("Data for fishbone diagram chart, such as, { name: 'main topic', children: [{ name: 'topic 1', children: [{ name: 'subtopic 1-1' }] }.").refine(validator_1.validatedTreeDataSchema, {
        message: "Invalid parameters: node name is not unique.",
        path: ["data"],
    }),
    theme: base_1.ThemeSchema,
    width: base_1.WidthSchema,
    height: base_1.HeightSchema,
};
// Fishbone diagram tool descriptor
const tool = {
    name: "generate_fishbone_diagram",
    description: "Generate a fishbone diagram chart to uses a fish skeleton, like structure to display the causes or effects of a core problem, with the problem as the fish head and the causes/effects as the fish bones. It suits problems that can be split into multiple related factors.",
    inputSchema: (0, utils_1.zodToJsonSchema)(schema),
};
exports.fishboneDiagram = {
    schema,
    tool,
};
